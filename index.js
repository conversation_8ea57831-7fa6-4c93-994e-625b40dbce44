import axios from "axios";
import { Telegraf } from "telegraf";
import OpenAI from "openai";
import { ChartJSNodeCanvas } from "chartjs-node-canvas";
import { RSI, EMA, WMA } from "technicalindicators";
import fs from "fs";
import dotenv from "dotenv";
import dayjs from "dayjs";

// Chart.js + plugins
import {
  Chart,
  TimeScale,
  LinearScale,
  BarController,
  BarElement,
  LineController,
  LineElement,
  PointElement,
  Tooltip,
  Legend,
  Title,
} from "chart.js";
// Note: Using linear scale for now due to date adapter issues in ES modules

// Dùng canvas để ghép nhiều biểu đồ
import { createCanvas, loadImage } from "canvas";

dotenv.config();

// === Config ===
const BINANCE_API = "https://api.binance.com/api/v3/klines";
const SYMBOL = process.env.SYMBOL || "ETHUSDT";
const INTERVAL = process.env.INTERVAL || "1h";
const bot = new Telegraf(process.env.TELEGRAM_BOT_TOKEN);
const chatId = process.env.TELEGRAM_GROUP_ID;
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// === Multi-Timeframe Config ===
const TIMEFRAMES = {
  '15m': { interval: '15m', limit: 96, name: '15-minute' },   // 24 hours of 15m candles
  '1h': { interval: '1h', limit: 168, name: '1-hour' },      // 7 days of 1h candles
  '4h': { interval: '4h', limit: 180, name: '4-hour' }       // 30 days of 4h candles
};

// Data cache for multi-timeframe analysis
const dataCache = {
  '15m': { data: null, lastUpdate: 0, ttl: 15 * 60 * 1000 },  // 15 min TTL
  '1h': { data: null, lastUpdate: 0, ttl: 60 * 60 * 1000 },   // 1 hour TTL
  '4h': { data: null, lastUpdate: 0, ttl: 4 * 60 * 60 * 1000 } // 4 hour TTL
};

// === Custom Candlestick Plugin ===
const CandlestickPlugin = {
  id: 'candlestick',
  afterDatasetsDraw(chart) {
    const { ctx, data, scales } = chart;
    const dataset = data.datasets.find(ds => ds.label && ds.label.includes(SYMBOL));
    if (!dataset || !dataset.data[0] || dataset.data[0].o === undefined) return;

    const xScale = scales.x;
    const yScale = scales.y;

    dataset.data.forEach((candle, index) => {
      if (!candle || candle.o === undefined) return;

      const x = xScale.getPixelForValue(candle.x);
      const yOpen = yScale.getPixelForValue(candle.o);
      const yHigh = yScale.getPixelForValue(candle.h);
      const yLow = yScale.getPixelForValue(candle.l);
      const yClose = yScale.getPixelForValue(candle.c);

      const isUp = candle.c >= candle.o;
      const candleWidth = Math.max(2, (xScale.width / dataset.data.length) * 0.6);

      // TradingView-style colors
      const upColor = '#26a69a';      // Green for bullish candles
      const downColor = '#ef5350';    // Red for bearish candles
      const upBorderColor = '#26a69a';
      const downBorderColor = '#ef5350';

      ctx.save();

      // Draw the wick (high-low line)
      ctx.strokeStyle = isUp ? upColor : downColor;
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(x, yHigh);
      ctx.lineTo(x, yLow);
      ctx.stroke();

      // Draw the body (open-close rectangle)
      const bodyTop = Math.min(yOpen, yClose);
      const bodyHeight = Math.max(1, Math.abs(yClose - yOpen)); // Ensure minimum height

      if (isUp) {
        // Bullish candle - filled with up color
        ctx.fillStyle = upColor;
        ctx.fillRect(x - candleWidth/2, bodyTop, candleWidth, bodyHeight);
        // Add subtle border
        ctx.strokeStyle = upBorderColor;
        ctx.lineWidth = 0.5;
        ctx.strokeRect(x - candleWidth/2, bodyTop, candleWidth, bodyHeight);
      } else {
        // Bearish candle - filled with down color
        ctx.fillStyle = downColor;
        ctx.fillRect(x - candleWidth/2, bodyTop, candleWidth, bodyHeight);
        // Add subtle border
        ctx.strokeStyle = downBorderColor;
        ctx.lineWidth = 0.5;
        ctx.strokeRect(x - candleWidth/2, bodyTop, candleWidth, bodyHeight);
      }

      ctx.restore();
    });
  }
};

// === Common chart register (gọi trong chartCallback của ChartJSNodeCanvas) ===
const registerAll = (Chart) => {
  Chart.register(
    TimeScale,
    LinearScale,
    BarController,
    BarElement,
    LineController,
    LineElement,
    PointElement,
    Tooltip,
    Legend,
    Title,
    CandlestickPlugin
  );
};

// === Multi-Timeframe Data Fetching ===
async function fetchTimeframeData(timeframe) {
  const config = TIMEFRAMES[timeframe];
  if (!config) {
    throw new Error(`Invalid timeframe: ${timeframe}`);
  }

  try {
    const { data } = await axios.get(BINANCE_API, {
      params: {
        symbol: SYMBOL,
        interval: config.interval,
        limit: config.limit
      },
    });

    return data.map((d) => ({
      time: d[0],
      open: parseFloat(d[1]),
      high: parseFloat(d[2]),
      low: parseFloat(d[3]),
      close: parseFloat(d[4]),
      volume: parseFloat(d[5]),
      closeTime: d[6],
      timeframe: timeframe
    }));
  } catch (error) {
    console.error(`Error fetching ${timeframe} data:`, error.message);
    throw error;
  }
}

// === Cached Data Fetching ===
async function getCachedData(timeframe) {
  const cache = dataCache[timeframe];
  const now = Date.now();

  // Check if cache is valid
  if (cache.data && (now - cache.lastUpdate) < cache.ttl) {
    console.log(`Using cached data for ${timeframe}`);
    return cache.data;
  }

  // Fetch fresh data
  console.log(`Fetching fresh data for ${timeframe}`);
  const data = await fetchTimeframeData(timeframe);

  // Update cache
  cache.data = data;
  cache.lastUpdate = now;

  return data;
}

// === Fetch All Timeframes ===
async function fetchAllTimeframes() {
  try {
    const [data15m, data1h, data4h] = await Promise.all([
      getCachedData('15m'),
      getCachedData('1h'),
      getCachedData('4h')
    ]);

    return {
      '15m': data15m,
      '1h': data1h,
      '4h': data4h
    };
  } catch (error) {
    console.error('Error fetching multi-timeframe data:', error);
    throw error;
  }
}

// === Legacy fetchData function (for backward compatibility) ===
async function fetchData() {
  const { data } = await axios.get(BINANCE_API, {
    params: { symbol: SYMBOL, interval: INTERVAL, limit: 240 },
  });

  return data.map((d) => ({
    time: d[0],
    open: parseFloat(d[1]),
    high: parseFloat(d[2]),
    low: parseFloat(d[3]),
    close: parseFloat(d[4]),
    volume: parseFloat(d[5]),
    closeTime: d[6],
  }));
}

// === Indicators (EMA, Sonic R PAC, RSI + EMA9 + WMA45) ===
function padToLen(arr, len) {
  return Array(len - arr.length).fill(null).concat(arr);
}

function calculateIndicators(candles) {
  const closes = candles.map((c) => c.close);
  const highs = candles.map((c) => c.high);
  const lows = candles.map((c) => c.low);
  const len = candles.length;

  // Sonic R PAC (EMA 34 của high/low/close)
  const pacLen = 34;
  const pacC = padToLen(EMA.calculate({ values: closes, period: pacLen }), len);
  const pacH = padToLen(EMA.calculate({ values: highs, period: pacLen }), len);
  const pacL = padToLen(EMA.calculate({ values: lows, period: pacLen }), len);

  // EMA
  const ema20 = padToLen(EMA.calculate({ values: closes, period: 20 }), len);
  const ema50 = padToLen(EMA.calculate({ values: closes, period: 50 }), len);
  const ema89 = padToLen(EMA.calculate({ values: closes, period: 89 }), len);
  const ema200 = padToLen(EMA.calculate({ values: closes, period: 200 }), len);
  const ema610 = padToLen(EMA.calculate({ values: closes, period: 610 }), len);

  // RSI + EMA9 + WMA45
  const rsi14 = RSI.calculate({ values: closes, period: 14 });
  const rsi = padToLen(rsi14, len);
  const rsiEma9 = padToLen(EMA.calculate({ values: rsi14, period: 9 }), len);
  const rsiWma45 = padToLen(WMA.calculate({ values: rsi14, period: 45 }), len);

  return { closes, pacC, pacH, pacL, ema20, ema50, ema89, ema200, ema610, rsi, rsiEma9, rsiWma45 };
}

// === Trend Analysis Engine ===
function analyzeTrend(candles, indicators) {
  if (!candles || candles.length < 50) {
    return { trend: 'INSUFFICIENT_DATA', strength: 0, confidence: 0 };
  }

  const { ema20, ema50, ema89, ema200, closes, rsi } = indicators;
  const currentPrice = closes[closes.length - 1];
  const currentEma20 = ema20[ema20.length - 1];
  const currentEma50 = ema50[ema50.length - 1];
  const currentEma89 = ema89[ema89.length - 1];
  const currentEma200 = ema200[ema200.length - 1];
  const currentRsi = rsi[rsi.length - 1];

  // EMA alignment analysis
  const emaAlignment = analyzeEmaAlignment(currentPrice, currentEma20, currentEma50, currentEma89, currentEma200);

  // Price action analysis
  const priceAction = analyzePriceAction(candles.slice(-20)); // Last 20 candles

  // RSI momentum analysis
  const rsiMomentum = analyzeRsiMomentum(currentRsi);

  // Volume analysis
  const volumeAnalysis = analyzeVolume(candles.slice(-10)); // Last 10 candles

  // Combine all factors to determine trend
  const trendScore = calculateTrendScore(emaAlignment, priceAction, rsiMomentum, volumeAnalysis);

  return {
    trend: determineTrend(trendScore.score),
    strength: Math.abs(trendScore.score),
    confidence: trendScore.confidence,
    details: {
      emaAlignment,
      priceAction,
      rsiMomentum,
      volumeAnalysis,
      score: trendScore.score
    }
  };
}

function analyzeEmaAlignment(price, ema20, ema50, ema89, ema200) {
  let score = 0;
  let alignmentCount = 0;

  // Check EMA hierarchy for bullish/bearish alignment
  if (ema20 > ema50) { score += 1; alignmentCount++; }
  if (ema50 > ema89) { score += 1; alignmentCount++; }
  if (ema89 > ema200) { score += 1; alignmentCount++; }
  if (price > ema20) { score += 2; alignmentCount += 2; } // Price above short EMA is more important

  // Check for bearish alignment
  if (ema20 < ema50) { score -= 1; alignmentCount++; }
  if (ema50 < ema89) { score -= 1; alignmentCount++; }
  if (ema89 < ema200) { score -= 1; alignmentCount++; }
  if (price < ema20) { score -= 2; alignmentCount += 2; }

  return {
    score: alignmentCount > 0 ? score / alignmentCount : 0,
    strength: Math.abs(score) / Math.max(alignmentCount, 1)
  };
}

function analyzePriceAction(recentCandles) {
  if (recentCandles.length < 10) return { score: 0, pattern: 'INSUFFICIENT_DATA' };

  let higherHighs = 0;
  let lowerLows = 0;
  let bullishCandles = 0;
  let bearishCandles = 0;

  for (let i = 1; i < recentCandles.length; i++) {
    const current = recentCandles[i];
    const previous = recentCandles[i - 1];

    // Count higher highs and lower lows
    if (current.high > previous.high) higherHighs++;
    if (current.low < previous.low) lowerLows++;

    // Count bullish/bearish candles
    if (current.close > current.open) bullishCandles++;
    if (current.close < current.open) bearishCandles++;
  }

  const totalCandles = recentCandles.length - 1;
  const hhPercentage = higherHighs / totalCandles;
  const llPercentage = lowerLows / totalCandles;
  const bullishPercentage = bullishCandles / totalCandles;

  let score = 0;
  let pattern = 'SIDEWAYS';

  if (hhPercentage > 0.6 && bullishPercentage > 0.6) {
    score = (hhPercentage + bullishPercentage) / 2;
    pattern = 'BULLISH_MOMENTUM';
  } else if (llPercentage > 0.6 && bullishPercentage < 0.4) {
    score = -((llPercentage + (1 - bullishPercentage)) / 2);
    pattern = 'BEARISH_MOMENTUM';
  }

  return { score, pattern, higherHighs, lowerLows, bullishCandles, bearishCandles };
}

function analyzeRsiMomentum(rsi) {
  if (!rsi || rsi < 0 || rsi > 100) return { score: 0, condition: 'INVALID' };

  let score = 0;
  let condition = 'NEUTRAL';

  if (rsi > 70) {
    score = -0.5; // Overbought - potential bearish
    condition = 'OVERBOUGHT';
  } else if (rsi < 30) {
    score = 0.5; // Oversold - potential bullish
    condition = 'OVERSOLD';
  } else if (rsi > 50) {
    score = (rsi - 50) / 100; // Bullish momentum
    condition = 'BULLISH_MOMENTUM';
  } else {
    score = (rsi - 50) / 100; // Bearish momentum
    condition = 'BEARISH_MOMENTUM';
  }

  return { score, condition, value: rsi };
}

function analyzeVolume(recentCandles) {
  if (recentCandles.length < 5) return { score: 0, trend: 'INSUFFICIENT_DATA' };

  const volumes = recentCandles.map(c => c.volume);
  const avgVolume = volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length;
  const recentVolume = volumes[volumes.length - 1];

  let score = 0;
  let trend = 'NORMAL';

  if (recentVolume > avgVolume * 1.5) {
    score = 0.3; // High volume supports trend
    trend = 'HIGH_VOLUME';
  } else if (recentVolume < avgVolume * 0.5) {
    score = -0.1; // Low volume weakens trend
    trend = 'LOW_VOLUME';
  }

  return { score, trend, current: recentVolume, average: avgVolume };
}

function calculateTrendScore(emaAlignment, priceAction, rsiMomentum, volumeAnalysis) {
  // Weighted combination of all factors
  const weights = {
    ema: 0.4,      // EMA alignment is most important
    price: 0.3,    // Price action is second
    rsi: 0.2,      // RSI momentum
    volume: 0.1    // Volume confirmation
  };

  const score = (
    emaAlignment.score * weights.ema +
    priceAction.score * weights.price +
    rsiMomentum.score * weights.rsi +
    volumeAnalysis.score * weights.volume
  );

  // Calculate confidence based on alignment of factors
  const factors = [emaAlignment.score, priceAction.score, rsiMomentum.score];
  const avgFactor = factors.reduce((sum, f) => sum + f, 0) / factors.length;
  const variance = factors.reduce((sum, f) => sum + Math.pow(f - avgFactor, 2), 0) / factors.length;
  const confidence = Math.max(0, Math.min(1, 1 - variance)); // Lower variance = higher confidence

  return { score, confidence };
}

function determineTrend(score) {
  if (score > 0.3) return 'BULLISH';
  if (score < -0.3) return 'BEARISH';
  return 'SIDEWAYS';
}

// === Multi-Timeframe Analysis ===
async function performMultiTimeframeAnalysis() {
  try {
    // Fetch all timeframe data
    const allData = await fetchAllTimeframes();

    // Calculate indicators for each timeframe
    const analysis = {};
    for (const [timeframe, candles] of Object.entries(allData)) {
      const indicators = calculateIndicators(candles);
      const trend = analyzeTrend(candles, indicators);

      analysis[timeframe] = {
        candles,
        indicators,
        trend,
        timeframeName: TIMEFRAMES[timeframe].name
      };
    }

    // Add risk levels to 15m analysis
    if (analysis['15m'] && analysis['1h']) {
      const majorTrend = { trend: 'BULLISH', strength: 0.5 }; // Temporary for risk calculation
      analysis['15m'].riskLevels = calculateRiskLevels(
        analysis['15m'].candles,
        analysis['1h'].candles,
        majorTrend
      );
    }

    return analysis;
  } catch (error) {
    console.error('Error in multi-timeframe analysis:', error);
    throw error;
  }
}

// === Hierarchical Trading Logic ===
function generateTradingSignal(multiTimeframeAnalysis) {
  const { '4h': tf4h, '1h': tf1h, '15m': tf15m } = multiTimeframeAnalysis;

  // Step 1: Determine major trend from 4H and 1H
  const majorTrend = determineMajorTrend(tf4h.trend, tf1h.trend);

  // Step 2: Check for trend alignment
  const trendAlignment = checkTrendAlignment(tf4h.trend, tf1h.trend, tf15m.trend);

  // Step 3: Generate entry signal based on 15M timeframe
  const entrySignal = generateEntrySignal(tf15m, majorTrend);

  // Step 4: Calculate risk management levels
  const riskLevels = calculateRiskLevels(tf15m.candles, tf1h.candles, majorTrend);

  return {
    majorTrend,
    trendAlignment,
    entrySignal,
    riskLevels,
    confidence: calculateOverallConfidence(tf4h.trend, tf1h.trend, tf15m.trend),
    recommendation: generateTradeRecommendation(majorTrend, trendAlignment, entrySignal)
  };
}

function determineMajorTrend(trend4h, trend1h) {
  // 4H trend has higher weight than 1H
  const weights = { '4h': 0.7, '1h': 0.3 };

  // Convert trends to numeric scores
  const trendToScore = { 'BULLISH': 1, 'SIDEWAYS': 0, 'BEARISH': -1 };

  const score4h = trendToScore[trend4h.trend] || 0;
  const score1h = trendToScore[trend1h.trend] || 0;

  const weightedScore = (score4h * weights['4h']) + (score1h * weights['1h']);

  let majorTrend = 'SIDEWAYS';
  if (weightedScore > 0.3) majorTrend = 'BULLISH';
  else if (weightedScore < -0.3) majorTrend = 'BEARISH';

  return {
    trend: majorTrend,
    strength: Math.abs(weightedScore),
    components: {
      '4h': { trend: trend4h.trend, strength: trend4h.strength, confidence: trend4h.confidence },
      '1h': { trend: trend1h.trend, strength: trend1h.strength, confidence: trend1h.confidence }
    }
  };
}

function checkTrendAlignment(trend4h, trend1h, trend15m) {
  const trends = [trend4h.trend, trend1h.trend, trend15m.trend];
  const bullishCount = trends.filter(t => t === 'BULLISH').length;
  const bearishCount = trends.filter(t => t === 'BEARISH').length;
  const sidewaysCount = trends.filter(t => t === 'SIDEWAYS').length;

  let alignment = 'MIXED';
  let strength = 0;

  if (bullishCount >= 2) {
    alignment = 'BULLISH_ALIGNED';
    strength = bullishCount / 3;
  } else if (bearishCount >= 2) {
    alignment = 'BEARISH_ALIGNED';
    strength = bearishCount / 3;
  } else if (sidewaysCount >= 2) {
    alignment = 'SIDEWAYS_ALIGNED';
    strength = sidewaysCount / 3;
  }

  return {
    alignment,
    strength,
    details: {
      bullish: bullishCount,
      bearish: bearishCount,
      sideways: sidewaysCount
    }
  };
}

function generateEntrySignal(tf15m, majorTrend) {
  const { trend, indicators, candles } = tf15m;
  const currentPrice = candles[candles.length - 1].close;
  const ema20 = indicators.ema20[indicators.ema20.length - 1];
  const ema50 = indicators.ema50[indicators.ema50.length - 1];
  const rsi = indicators.rsi[indicators.rsi.length - 1];

  let signal = 'NO_SIGNAL';
  let entryPrice = null;
  let reasoning = [];

  // Only generate signals that align with major trend
  if (majorTrend.trend === 'BULLISH' && trend.trend !== 'BEARISH') {
    // Look for bullish entry conditions
    if (currentPrice > ema20 && ema20 > ema50 && rsi > 40 && rsi < 70) {
      signal = 'LONG';
      entryPrice = currentPrice;
      reasoning.push('Price above EMA20', 'EMA20 > EMA50', 'RSI in bullish range');
    } else if (currentPrice < ema20 && currentPrice > ema50 && rsi < 50) {
      signal = 'LONG_PULLBACK';
      entryPrice = ema20; // Wait for pullback to EMA20
      reasoning.push('Pullback to EMA20 in uptrend', 'RSI oversold');
    }
  } else if (majorTrend.trend === 'BEARISH' && trend.trend !== 'BULLISH') {
    // Look for bearish entry conditions
    if (currentPrice < ema20 && ema20 < ema50 && rsi < 60 && rsi > 30) {
      signal = 'SHORT';
      entryPrice = currentPrice;
      reasoning.push('Price below EMA20', 'EMA20 < EMA50', 'RSI in bearish range');
    } else if (currentPrice > ema20 && currentPrice < ema50 && rsi > 50) {
      signal = 'SHORT_PULLBACK';
      entryPrice = ema20; // Wait for pullback to EMA20
      reasoning.push('Pullback to EMA20 in downtrend', 'RSI overbought');
    }
  }

  return {
    signal,
    entryPrice,
    reasoning,
    currentPrice,
    keyLevels: {
      ema20,
      ema50,
      rsi
    }
  };
}

function calculateRiskLevels(candles15m, candles1h, majorTrend) {
  const current15m = candles15m[candles15m.length - 1];
  const recent15m = candles15m.slice(-20); // Last 20 candles for support/resistance
  const recent1h = candles1h.slice(-10);   // Last 10 1H candles for major levels

  // Calculate support and resistance levels
  const support = findSupportLevel(recent15m, recent1h);
  const resistance = findResistanceLevel(recent15m, recent1h);

  // Calculate stop loss and take profit based on trend
  let stopLoss, takeProfit1, takeProfit2;
  const atr = calculateATR(recent15m); // Average True Range for volatility

  if (majorTrend.trend === 'BULLISH') {
    stopLoss = Math.max(support, current15m.close - (atr * 2));
    takeProfit1 = current15m.close + (atr * 2);
    takeProfit2 = Math.min(resistance, current15m.close + (atr * 4));
  } else if (majorTrend.trend === 'BEARISH') {
    stopLoss = Math.min(resistance, current15m.close + (atr * 2));
    takeProfit1 = current15m.close - (atr * 2);
    takeProfit2 = Math.max(support, current15m.close - (atr * 4));
  } else {
    // Sideways market - tighter stops
    stopLoss = current15m.close + (majorTrend.trend === 'BULLISH' ? -atr : atr);
    takeProfit1 = current15m.close + (majorTrend.trend === 'BULLISH' ? atr : -atr);
    takeProfit2 = takeProfit1;
  }

  return {
    support,
    resistance,
    stopLoss,
    takeProfit1,
    takeProfit2,
    atr,
    riskReward: Math.abs(takeProfit1 - current15m.close) / Math.abs(stopLoss - current15m.close)
  };
}

function findSupportLevel(candles15m, candles1h) {
  const lows15m = candles15m.map(c => c.low);
  const lows1h = candles1h.map(c => c.low);

  // Find recent significant lows
  const support15m = Math.min(...lows15m.slice(-10));
  const support1h = Math.min(...lows1h.slice(-5));

  return Math.min(support15m, support1h);
}

function findResistanceLevel(candles15m, candles1h) {
  const highs15m = candles15m.map(c => c.high);
  const highs1h = candles1h.map(c => c.high);

  // Find recent significant highs
  const resistance15m = Math.max(...highs15m.slice(-10));
  const resistance1h = Math.max(...highs1h.slice(-5));

  return Math.max(resistance15m, resistance1h);
}

function calculateATR(candles, period = 14) {
  if (candles.length < period + 1) return 0;

  const trueRanges = [];
  for (let i = 1; i < candles.length; i++) {
    const current = candles[i];
    const previous = candles[i - 1];

    const tr = Math.max(
      current.high - current.low,
      Math.abs(current.high - previous.close),
      Math.abs(current.low - previous.close)
    );
    trueRanges.push(tr);
  }

  // Calculate average of last 'period' true ranges
  const recentTR = trueRanges.slice(-period);
  return recentTR.reduce((sum, tr) => sum + tr, 0) / recentTR.length;
}

function calculateOverallConfidence(trend4h, trend1h, trend15m) {
  const confidences = [trend4h.confidence, trend1h.confidence, trend15m.confidence];
  const avgConfidence = confidences.reduce((sum, c) => sum + c, 0) / confidences.length;

  // Boost confidence if trends align
  const trends = [trend4h.trend, trend1h.trend, trend15m.trend];
  const uniqueTrends = [...new Set(trends)];
  const alignmentBonus = uniqueTrends.length === 1 ? 0.2 : uniqueTrends.length === 2 ? 0.1 : 0;

  return Math.min(1, avgConfidence + alignmentBonus);
}

function generateTradeRecommendation(majorTrend, trendAlignment, entrySignal) {
  let recommendation = 'NO_TRADE';
  let reasoning = [];

  // High confidence trades
  if (trendAlignment.alignment.includes('ALIGNED') && entrySignal.signal !== 'NO_SIGNAL') {
    if (majorTrend.trend === 'BULLISH' && entrySignal.signal.includes('LONG')) {
      recommendation = 'STRONG_LONG';
      reasoning.push('Multi-timeframe bullish alignment', 'Clear long entry signal');
    } else if (majorTrend.trend === 'BEARISH' && entrySignal.signal.includes('SHORT')) {
      recommendation = 'STRONG_SHORT';
      reasoning.push('Multi-timeframe bearish alignment', 'Clear short entry signal');
    }
  }
  // Medium confidence trades
  else if (majorTrend.strength > 0.5 && entrySignal.signal !== 'NO_SIGNAL') {
    if (majorTrend.trend === 'BULLISH' && entrySignal.signal.includes('LONG')) {
      recommendation = 'MODERATE_LONG';
      reasoning.push('Strong major trend', 'Partial alignment');
    } else if (majorTrend.trend === 'BEARISH' && entrySignal.signal.includes('SHORT')) {
      recommendation = 'MODERATE_SHORT';
      reasoning.push('Strong major trend', 'Partial alignment');
    }
  }
  // Wait for better setup
  else {
    recommendation = 'WAIT';
    reasoning.push('Mixed signals', 'Wait for better alignment');
  }

  return {
    action: recommendation,
    reasoning,
    confidence: calculateTradeConfidence(majorTrend, trendAlignment, entrySignal)
  };
}

function calculateTradeConfidence(majorTrend, trendAlignment, entrySignal) {
  let confidence = 0;

  // Major trend strength
  confidence += majorTrend.strength * 0.4;

  // Trend alignment
  confidence += trendAlignment.strength * 0.3;

  // Entry signal quality
  if (entrySignal.signal !== 'NO_SIGNAL') {
    confidence += 0.2;
    if (entrySignal.reasoning.length >= 2) confidence += 0.1;
  }

  return Math.min(1, confidence);
}

// === Helpers ===
function inferTimeUnit(interval) {
  const s = interval.toLowerCase();
  if (s.endsWith("m")) return "minute";
  if (s.endsWith("h")) return "hour";
  if (s.endsWith("d")) return "day";
  return "hour";
}

// === Enhanced Multi-Timeframe Chart Rendering ===
async function renderPricePanel(candles, ind, width = 1200, height = 560, multiTimeframeData = null) {
  const canvas = new ChartJSNodeCanvas({
    width,
    height,
    backgroundColour: "#141416",
    chartCallback: registerAll,
  });

  const candleData = candles.map((c, i) => ({ x: i, o: c.open, h: c.high, l: c.low, c: c.close, time: c.time }));

  const config = {
    type: "line", // Use line as base type, candlesticks will be drawn by plugin
    data: {
      datasets: [
        {
          type: "line",
          label: `${SYMBOL} ${INTERVAL}`,
          data: candleData,
          showLine: false,
          pointRadius: 0,
          borderWidth: 0,
          backgroundColor: 'transparent',
        },
        // Sonic R PAC
        {
          type: "line",
          label: "PAC High (EMA34)",
          data: candles.map((_, i) => ({ x: i, y: ind.pacH[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 1,
          borderColor: '#ff9800',
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "PAC Low (EMA34)",
          data: candles.map((_, i) => ({ x: i, y: ind.pacL[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 1,
          borderColor: '#ff5722',
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "PAC Close (EMA34)",
          data: candles.map((_, i) => ({ x: i, y: ind.pacC[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 2,
          borderColor: '#9c27b0',
          backgroundColor: 'transparent'
        },
        // EMA overlays
        {
          type: "line",
          label: "EMA20",
          data: candles.map((_, i) => ({ x: i, y: ind.ema20[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 2,
          borderColor: '#2196f3',
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "EMA50",
          data: candles.map((_, i) => ({ x: i, y: ind.ema50[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 2,
          borderColor: '#4caf50',
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "EMA89",
          data: candles.map((_, i) => ({ x: i, y: ind.ema89[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 2,
          borderColor: '#ffeb3b',
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "EMA200",
          data: candles.map((_, i) => ({ x: i, y: ind.ema200[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 2,
          borderColor: '#f44336',
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "EMA610",
          data: candles.map((_, i) => ({ x: i, y: ind.ema610[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 1,
          borderColor: '#795548',
          backgroundColor: 'transparent'
        },
        // Multi-timeframe trend indicators (if available)
        ...(multiTimeframeData ? createMultiTimeframeTrendIndicators(candles, multiTimeframeData) : [])
      ],
    },
    options: {
      responsive: false,
      plugins: {
        title: {
          display: true,
          text: `${SYMBOL} ${INTERVAL.toUpperCase()} Binance`,
          color: "#FFFFFF",
          font: { size: 18, weight: "bold" },
        },
        legend: {
          display: true,
          position: "top",
          labels: { color: "#DDD" },
        },
        tooltip: {
          enabled: true,
          callbacks: {
            title: function(context) {
              const index = context[0].dataIndex;
              return dayjs(candles[index].time).format('YYYY-MM-DD HH:mm');
            },
            label: function(context) {
              const index = context.dataIndex;
              const candle = candleData[index];
              if (candle && candle.o !== undefined) {
                return [
                  `Open: ${candle.o.toFixed(4)}`,
                  `High: ${candle.h.toFixed(4)}`,
                  `Low: ${candle.l.toFixed(4)}`,
                  `Close: ${candle.c.toFixed(4)}`
                ];
              }
              return context.dataset.label + ': ' + context.parsed.y;
            }
          }
        },
      },
      scales: {
        x: {
          type: "linear",
          ticks: {
            color: "#AAA",
            maxTicksLimit: 12,
            callback: function(value, index) {
              // Show time labels for every few ticks
              if (index % Math.ceil(candles.length / 8) === 0 && candles[value]) {
                return dayjs(candles[value].time).format('MM/DD HH:mm');
              }
              return '';
            }
          },
          grid: { color: "rgba(255,255,255,0.08)" },
        },
        y: {
          position: "left",
          ticks: { color: "#AAA" },
          grid: { color: "rgba(255,255,255,0.08)" },
        },
      },
    },
  };

  return canvas.renderToBuffer(config, "image/png");
}

// === Multi-Timeframe Chart Indicators ===
function createMultiTimeframeTrendIndicators(candles, multiTimeframeData) {
  const indicators = [];

  // Add trend direction indicators at the top of the chart
  const highestPrice = Math.max(...candles.map(c => c.high));
  const lowestPrice = Math.min(...candles.map(c => c.low));
  const priceRange = highestPrice - lowestPrice;

  // Position trend indicators at different levels
  const trendLevels = {
    '4h': highestPrice + (priceRange * 0.05),
    '1h': highestPrice + (priceRange * 0.03),
    '15m': highestPrice + (priceRange * 0.01)
  };

  // Create trend indicator lines for each timeframe
  Object.entries(multiTimeframeData).forEach(([timeframe, data]) => {
    const trend = data.trend.trend;
    const confidence = data.trend.confidence;

    // Color based on trend
    let color = '#666666'; // Default gray
    if (trend === 'BULLISH') color = `rgba(76, 175, 80, ${confidence})`;
    else if (trend === 'BEARISH') color = `rgba(244, 67, 54, ${confidence})`;
    else color = `rgba(255, 193, 7, ${confidence})`;

    // Add horizontal line showing trend
    indicators.push({
      type: "line",
      label: `${timeframe.toUpperCase()} Trend: ${trend}`,
      data: candles.map((_, i) => ({ x: i, y: trendLevels[timeframe] })),
      borderWidth: 3,
      pointRadius: 0,
      borderColor: color,
      backgroundColor: 'transparent',
      borderDash: trend === 'SIDEWAYS' ? [5, 5] : []
    });
  });

  // Add support and resistance levels if available
  if (multiTimeframeData['15m'] && multiTimeframeData['15m'].riskLevels) {
    const riskLevels = multiTimeframeData['15m'].riskLevels;

    // Support level
    if (riskLevels.support) {
      indicators.push({
        type: "line",
        label: "Support",
        data: candles.map((_, i) => ({ x: i, y: riskLevels.support })),
        borderWidth: 2,
        pointRadius: 0,
        borderColor: 'rgba(76, 175, 80, 0.7)',
        backgroundColor: 'transparent',
        borderDash: [10, 5]
      });
    }

    // Resistance level
    if (riskLevels.resistance) {
      indicators.push({
        type: "line",
        label: "Resistance",
        data: candles.map((_, i) => ({ x: i, y: riskLevels.resistance })),
        borderWidth: 2,
        pointRadius: 0,
        borderColor: 'rgba(244, 67, 54, 0.7)',
        backgroundColor: 'transparent',
        borderDash: [10, 5]
      });
    }
  }

  return indicators;
}

async function renderRsiPanel(candles, ind, width = 1200, height = 180) {
  const canvas = new ChartJSNodeCanvas({
    width,
    height,
    backgroundColour: "#141416",
    chartCallback: registerAll,
  });

  const config = {
    type: "line",
    data: {
      datasets: [
        {
          label: "RSI Upper 80",
          data: candles.map((_, i) => ({ x: i, y: 80 })),
          borderWidth: 1,
          pointRadius: 0,
          borderColor: '#666',
          backgroundColor: 'transparent'
        },
        {
          label: "RSI Lower 20",
          data: candles.map((_, i) => ({ x: i, y: 20 })),
          borderWidth: 1,
          pointRadius: 0,
          borderColor: '#666',
          backgroundColor: 'transparent'
        },
        {
          label: "RSI(14)",
          data: candles.map((_, i) => ({ x: i, y: ind.rsi[i] })),
          borderWidth: 2,
          pointRadius: 0,
          borderColor: '#2196f3',
          backgroundColor: 'transparent'
        },
        {
          label: "EMA9(RSI)",
          data: candles.map((_, i) => ({ x: i, y: ind.rsiEma9[i] })),
          borderWidth: 1,
          pointRadius: 0,
          borderColor: '#ff9800',
          backgroundColor: 'transparent'
        },
        {
          label: "WMA45(RSI)",
          data: candles.map((_, i) => ({ x: i, y: ind.rsiWma45[i] })),
          borderWidth: 2,
          pointRadius: 0,
          borderColor: '#9c27b0',
          backgroundColor: 'transparent'
        },
      ],
    },
    options: {
      responsive: false,
      plugins: {
        legend: { display: true, labels: { color: "#DDD" } },
        title: { display: true, text: "RSI Panel", color: "#FFF" },
      },
      scales: {
        x: {
          type: "linear",
          ticks: {
            color: "#AAA",
            maxTicksLimit: 12,
            callback: function(value, index) {
              if (index % Math.ceil(candles.length / 8) === 0 && candles[value]) {
                return dayjs(candles[value].time).format('MM/DD HH:mm');
              }
              return '';
            }
          },
          grid: { color: "rgba(255,255,255,0.05)" },
        },
        y: {
          min: 0,
          max: 100,
          ticks: { color: "#AAA" },
          grid: { color: "rgba(255,255,255,0.05)" },
        },
      },
    },
  };

  return canvas.renderToBuffer(config, "image/png");
}

async function renderVolumePanel(candles, width = 1200, height = 160) {
  const canvas = new ChartJSNodeCanvas({
    width,
    height,
    backgroundColour: "#141416",
    chartCallback: registerAll,
  });

  const volumeData = candles.map((c, i) => {
    const bgColor = i === 0 ? "rgba(38,166,154,0.6)" :
      (c.close >= candles[i - 1].close ? "rgba(38,166,154,0.6)" : "rgba(239,83,80,0.6)");
    return { x: i, y: c.volume, backgroundColor: bgColor };
  });

  const config = {
    type: "bar",
    data: {
      datasets: [
        {
          label: "Volume",
          data: volumeData,
          borderWidth: 0,
          backgroundColor: volumeData.map(d => d.backgroundColor)
        },
      ],
    },
    options: {
      responsive: false,
      plugins: {
        legend: { display: false },
        title: { display: true, text: "Volume", color: "#FFF" },
      },
      scales: {
        x: {
          type: "linear",
          ticks: {
            color: "#AAA",
            maxTicksLimit: 12,
            callback: function(value, index) {
              if (index % Math.ceil(candles.length / 8) === 0 && candles[value]) {
                return dayjs(candles[value].time).format('MM/DD HH:mm');
              }
              return '';
            }
          },
          grid: { color: "rgba(255,255,255,0.05)" },
        },
        y: {
          ticks: { color: "#AAA" },
          grid: { color: "rgba(255,255,255,0.05)" },
        },
      },
    },
  };

  return canvas.renderToBuffer(config, "image/png");
}

// === Compose 3 panels thành 1 ảnh ===
async function composePanels(priceBuf, rsiBuf, volBuf, outPath = "chart.png") {
  const priceImg = await loadImage(priceBuf);
  const rsiImg = await loadImage(rsiBuf);
  const volImg = await loadImage(volBuf);

  const gap = 6;
  const width = Math.max(priceImg.width, rsiImg.width, volImg.width);
  const height = priceImg.height + rsiImg.height + volImg.height + gap * 2;

  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext("2d");

  // background
  ctx.fillStyle = "#0f1012";
  ctx.fillRect(0, 0, width, height);

  let y = 0;
  ctx.drawImage(priceImg, 0, y); y += priceImg.height + gap;
  ctx.drawImage(rsiImg, 0, y);   y += rsiImg.height + gap;
  ctx.drawImage(volImg, 0, y);

  const buffer = canvas.toBuffer("image/png");
  fs.writeFileSync(outPath, buffer);
  return outPath;
}

// === Enhanced Multi-Timeframe GPT Analysis ===
async function analyzeWithGPT(multiTimeframeAnalysis, tradingSignal) {
  const { '15m': tf15m, '1h': tf1h, '4h': tf4h } = multiTimeframeAnalysis;
  const last15m = tf15m.candles.at(-1);

  // Format trend information
  const formatTrend = (trend) => {
    const trendEmoji = { 'BULLISH': '🟢', 'BEARISH': '🔴', 'SIDEWAYS': '⚪' };
    return `${trendEmoji[trend.trend] || '⚪'} ${trend.trend} (${(trend.strength * 100).toFixed(0)}%)`;
  };

  // Format trading signal
  const formatSignal = (signal) => {
    const signalEmoji = {
      'STRONG_LONG': '🟢🟢', 'MODERATE_LONG': '🟢',
      'STRONG_SHORT': '🔴🔴', 'MODERATE_SHORT': '🔴',
      'WAIT': '⚪', 'NO_TRADE': '⚪'
    };
    return `${signalEmoji[signal.action] || '⚪'} ${signal.action}`;
  };

  const text = `
You are a professional trading analyst. Analyze ${SYMBOL} using multi-timeframe analysis:

MULTI-TIMEFRAME TRENDS:
- 4H Trend: ${formatTrend(tf4h.trend)} (Confidence: ${(tf4h.trend.confidence * 100).toFixed(0)}%)
- 1H Trend: ${formatTrend(tf1h.trend)} (Confidence: ${(tf1h.trend.confidence * 100).toFixed(0)}%)
- 15M Trend: ${formatTrend(tf15m.trend)} (Confidence: ${(tf15m.trend.confidence * 100).toFixed(0)}%)

MAJOR TREND: ${formatTrend(tradingSignal.majorTrend)} (Strength: ${(tradingSignal.majorTrend.strength * 100).toFixed(0)}%)
ALIGNMENT: ${tradingSignal.trendAlignment.alignment} (${(tradingSignal.trendAlignment.strength * 100).toFixed(0)}%)

CURRENT DATA (15M):
- Price: ${last15m.close}
- EMA20/50/89: ${[tf15m.indicators.ema20.at(-1), tf15m.indicators.ema50.at(-1), tf15m.indicators.ema89.at(-1)].map(v=>v?.toFixed(2)).join("/")}
- RSI: ${tf15m.indicators.rsi.at(-1)?.toFixed(1)}
- Volume: ${last15m.volume}

TRADING SIGNAL: ${formatSignal(tradingSignal.recommendation)}
ENTRY SIGNAL: ${tradingSignal.entrySignal.signal}
RISK LEVELS: SL ${tradingSignal.riskLevels.stopLoss?.toFixed(2)} | TP1 ${tradingSignal.riskLevels.takeProfit1?.toFixed(2)} | RR ${tradingSignal.riskLevels.riskReward?.toFixed(2)}

RESPOND in this EXACT HTML format:

<b>📊 MULTI-TIMEFRAME ANALYSIS:</b>
• <b>Major Trend:</b> [4H+1H combined trend] - [Key reason]
• <b>Alignment:</b> [Timeframe alignment status]
• <b>15M Entry:</b> [Current 15M setup]

<b>🎯 TRADING RECOMMENDATION:</b>
• <b>Action:</b> [${tradingSignal.recommendation.action}]
• <b>Entry:</b> [Specific entry price/condition]
• <b>Stop Loss:</b> [SL level] | <b>Take Profit:</b> [TP levels]
• <b>Risk/Reward:</b> 1:${tradingSignal.riskLevels.riskReward?.toFixed(1)}

<b>💡 Confidence:</b> ${(tradingSignal.confidence * 100).toFixed(0)}%

IMPORTANT: Use only <b></b> HTML tags, no markdown. Be specific with prices. Max 500 characters.`;

  const res = await openai.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [
      { role: "system", content: "You are a professional trading analyst. MUST respond in EXACT HTML format requested. Use only <b></b> tags, no markdown. Be concise and professional." },
      { role: "user", content: text },
    ],
  });

  return res.choices[0].message.content;
}

// === Legacy GPT Analysis (for backward compatibility) ===
async function legacyAnalyzeWithGPT(candles, ind) {
  const last = candles.at(-1);

  const text = `
Bạn là chuyên gia phân tích kỹ thuật. Phân tích ${SYMBOL} khung ${INTERVAL} với dữ liệu:
- Giá hiện tại: ${last.close}
- EMA20/50/89: ${[ind.ema20.at(-1), ind.ema50.at(-1), ind.ema89.at(-1)].map(v=>v?.toFixed(2)).join("/")}
- RSI(14): ${ind.rsi.at(-1)?.toFixed(1)}
- Sonic R PAC: ${ind.pacC.at(-1)?.toFixed(2)}
- Volume: ${last.volume}

BẮT BUỘC trả lời theo format HTML chính xác sau:

<b>📈 XU HƯỚNG:</b> [Tăng/Giảm/Sideway] - [Lý do ngắn gọn]

<b>🎯 KỊCH BẢN TRADE:</b>
• <b>Loại:</b> [LONG 🟢 / SHORT 🔴 / NO TRADE ⚪]
• <b>Setup:</b> [Điều kiện entry cụ thể]
• <b>Xác nhận:</b> [Tín hiệu cần chờ]
• <b>Entry/SL/TP:</b> Entry [giá], SL [giá], TP [giá] (RR 1:[tỷ lệ])
• <b>Invalidation:</b> [Điều kiện hủy kèo]

<b>💡 Độ tin cậy:</b> [X]%

QUAN TRỌNG:
- Chỉ sử dụng thẻ HTML <b> và </b>
- Không dùng **, ##, hay markdown khác
- Giá phải cụ thể, không mơ hồ
- Giới hạn 600 ký tự`;

  const res = await openai.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [
      { role: "system", content: "Bạn là chuyên gia trading. BẮT BUỘC trả lời theo CHÍNH XÁC format HTML được yêu cầu. Chỉ sử dụng thẻ <b></b>, không dùng ** hay markdown. Ngắn gọn, chuyên nghiệp." },
      { role: "user", content: text },
    ],
  });

  return res.choices[0].message.content;
}





// === AI Vision-Powered Trading Bot ===
async function runBot() {
  try {
    console.log('🤖 Starting AI vision-powered trading analysis...');

    // Step 1: Perform multi-timeframe data collection
    const multiTimeframeAnalysis = await performMultiTimeframeAnalysis();

    // Step 2: AI Visual Analysis - Replace rule-based logic with AI vision
    const aiAnalysis = await performAIVisualAnalysis(multiTimeframeAnalysis);

    // Step 3: Generate AI-powered trading signal
    const aiTradingSignal = await generateAITradingSignal(aiAnalysis, multiTimeframeAnalysis);

    // Step 4: Create traditional chart for Telegram (15M with indicators)
    const candles15m = multiTimeframeAnalysis['15m'].candles;
    const indicators15m = multiTimeframeAnalysis['15m'].indicators;

    const [priceBuf, rsiBuf, volBuf] = await Promise.all([
      renderPricePanel(candles15m, indicators15m, 1200, 560, multiTimeframeAnalysis),
      renderRsiPanel(candles15m, indicators15m),
      renderVolumePanel(candles15m),
    ]);
    const chartPath = await composePanels(priceBuf, rsiBuf, volBuf, "chart.png");

    // Step 5: Enhanced GPT analysis with AI vision results
    const analysis = await analyzeWithAIVision(aiAnalysis, aiTradingSignal);

    // Step 6: Create comprehensive caption with AI insights
    const caption = createAIEnhancedCaption(aiTradingSignal, analysis);

    // Step 7: Send message with chart and AI analysis
    await bot.telegram.sendPhoto(
      chatId,
      { source: chartPath },
      {
        caption,
        parse_mode: 'HTML'
      }
    );

    console.log('✅ AI vision analysis completed successfully');
    console.log(`📊 AI Recommendation: ${aiTradingSignal.trading_recommendation.action}`);
    console.log(`🎯 AI Confidence: ${aiTradingSignal.overall_assessment.final_confidence}/10`);

  } catch (error) {
    console.error('❌ Error in AI vision bot:', error);

    // Fallback to rule-based multi-timeframe analysis
    try {
      console.log('🔄 Falling back to rule-based analysis...');
      await runRuleBasedBot();
    } catch (fallbackError) {
      console.error('❌ Rule-based fallback also failed:', fallbackError);

      // Final fallback to legacy single-timeframe
      try {
        console.log('🔄 Final fallback to legacy analysis...');
        await runLegacyBot();
      } catch (legacyError) {
        console.error('❌ All systems failed:', legacyError);

        // Send error notification
        await bot.telegram.sendMessage(
          chatId,
          `⚠️ <b>Critical Bot Error</b>\n\nAll analysis systems failed. Manual intervention required.\n\nPrimary Error: ${error.message}`,
          { parse_mode: 'HTML' }
        );
      }
    }
  }
}

function createEnhancedCaption(multiTimeframeAnalysis, tradingSignal, analysis) {
  const { majorTrend, trendAlignment, confidence } = tradingSignal;

  // Trend alignment indicators
  const alignmentEmoji = {
    'BULLISH_ALIGNED': '🟢🟢🟢',
    'BEARISH_ALIGNED': '🔴🔴🔴',
    'SIDEWAYS_ALIGNED': '⚪⚪⚪',
    'MIXED': '🟢🔴⚪'
  };

  const header = `📊 <b>${SYMBOL} - Multi-Timeframe Analysis</b>\n`;
  const alignment = `${alignmentEmoji[trendAlignment.alignment] || '⚪'} <b>Alignment:</b> ${trendAlignment.alignment} (${(confidence * 100).toFixed(0)}%)\n`;
  const majorTrendInfo = `📈 <b>Major Trend:</b> ${majorTrend.trend} (${(majorTrend.strength * 100).toFixed(0)}%)\n\n`;

  return header + alignment + majorTrendInfo + analysis;
}

// === AI-Enhanced Caption Creation ===
function createAIEnhancedCaption(aiTradingSignal, analysis) {
  const { major_trend, trading_recommendation, overall_assessment } = aiTradingSignal;

  // AI confidence indicators
  const confidenceEmoji = {
    10: '🟢🟢🟢', 9: '🟢🟢🟢', 8: '🟢🟢', 7: '🟢🟢',
    6: '🟢', 5: '🟡', 4: '🟡', 3: '🟠', 2: '🔴', 1: '🔴'
  };

  const confidence = overall_assessment.final_confidence;
  const confidenceIndicator = confidenceEmoji[confidence] || '⚪';

  const header = `🤖 <b>${SYMBOL} - AI Vision Analysis</b>\n`;
  const aiConfidence = `${confidenceIndicator} <b>AI Confidence:</b> ${confidence}/10 (${overall_assessment.trade_quality})\n`;
  const recommendation = `🎯 <b>AI Signal:</b> ${trading_recommendation.action}\n\n`;

  return header + aiConfidence + recommendation + analysis;
}

// === Rule-Based Fallback Bot ===
async function runRuleBasedBot() {
  console.log('🔄 Running rule-based multi-timeframe analysis...');

  // Perform multi-timeframe analysis
  const multiTimeframeAnalysis = await performMultiTimeframeAnalysis();

  // Generate trading signal using rule-based logic
  const tradingSignal = generateTradingSignal(multiTimeframeAnalysis);

  // Use 15M data for chart rendering
  const candles15m = multiTimeframeAnalysis['15m'].candles;
  const indicators15m = multiTimeframeAnalysis['15m'].indicators;

  // Render charts
  const [priceBuf, rsiBuf, volBuf] = await Promise.all([
    renderPricePanel(candles15m, indicators15m, 1200, 560, multiTimeframeAnalysis),
    renderRsiPanel(candles15m, indicators15m),
    renderVolumePanel(candles15m),
  ]);
  const chartPath = await composePanels(priceBuf, rsiBuf, volBuf, "chart.png");

  // Rule-based GPT analysis
  const analysis = await analyzeWithGPT(multiTimeframeAnalysis, tradingSignal);

  // Create caption
  const caption = createEnhancedCaption(multiTimeframeAnalysis, tradingSignal, analysis);

  // Send message
  await bot.telegram.sendPhoto(
    chatId,
    { source: chartPath },
    {
      caption,
      parse_mode: 'HTML'
    }
  );

  console.log('✅ Rule-based analysis completed successfully');
}

// === Legacy Bot Function (fallback) ===
async function runLegacyBot() {
  const candles = await fetchData();
  const ind = calculateIndicators(candles);

  // render 3 panel
  const [priceBuf, rsiBuf, volBuf] = await Promise.all([
    renderPricePanel(candles, ind),
    renderRsiPanel(candles, ind),
    renderVolumePanel(candles),
  ]);
  const chartPath = await composePanels(priceBuf, rsiBuf, volBuf, "chart.png");

  // analysis
  const analysis = await legacyAnalyzeWithGPT(candles, ind);

  // Send 1 message: photo + caption with HTML formatting
  await bot.telegram.sendPhoto(
    chatId,
    { source: chartPath },
    {
      caption: `📊 <b>${SYMBOL} (${INTERVAL})</b>\n\n${analysis}`,
      parse_mode: 'HTML'
    }
  );
}

// === Run every hour ===
setInterval(runBot, 60 * 60 * 1000);
runBot();

// === Exports for Testing ===
export {
  // Data functions
  fetchAllTimeframes,
  performMultiTimeframeAnalysis,
  calculateIndicators,
  analyzeTrend,
  fetchTimeframeData,
  getCachedData,
  TIMEFRAMES,
  dataCache,

  // Rule-based functions (legacy)
  generateTradingSignal,
  analyzeWithGPT,

  // AI Vision functions (new)
  generateCleanChartForAI,
  generateMultiTimeframeChartsForAI,
  analyzeChartWithAIVision,
  performAIVisualAnalysis,
  generateAITradingSignal,
  analyzeWithAIVision,

  // Bot functions
  runBot,
  runRuleBasedBot,
  runLegacyBot
};

// === AI Vision Chart Analysis System ===
async function generateCleanChartForAI(candles, indicators, timeframe, width = 1200, height = 800) {
  const canvas = new ChartJSNodeCanvas({
    width,
    height,
    backgroundColour: "#FFFFFF", // White background for better AI analysis
    chartCallback: registerAll,
  });

  const candleData = candles.map((c, i) => ({ x: i, o: c.open, h: c.high, l: c.low, c: c.close, time: c.time }));

  const config = {
    type: "line",
    data: {
      datasets: [
        // Candlestick data (will be drawn by plugin)
        {
          type: "line",
          label: `${SYMBOL} ${timeframe}`,
          data: candleData,
          showLine: false,
          pointRadius: 0,
          borderWidth: 0,
          backgroundColor: 'transparent',
        },
        // Key EMAs for trend analysis
        {
          type: "line",
          label: "EMA20",
          data: candles.map((_, i) => ({ x: i, y: indicators.ema20[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 3,
          borderColor: '#2196F3', // Blue
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "EMA50",
          data: candles.map((_, i) => ({ x: i, y: indicators.ema50[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 3,
          borderColor: '#FF9800', // Orange
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "EMA200",
          data: candles.map((_, i) => ({ x: i, y: indicators.ema200[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 3,
          borderColor: '#F44336', // Red
          backgroundColor: 'transparent'
        },
      ],
    },
    options: {
      responsive: false,
      plugins: {
        title: {
          display: true,
          text: `${SYMBOL} ${timeframe.toUpperCase()} - AI Analysis Chart`,
          color: "#000000",
          font: { size: 24, weight: "bold" },
        },
        legend: {
          display: true,
          position: "top",
          labels: {
            color: "#000000",
            font: { size: 14, weight: "bold" }
          },
        },
        tooltip: { enabled: false }, // Disable for cleaner image
      },
      scales: {
        x: {
          type: "linear",
          ticks: {
            color: "#000000",
            font: { size: 12, weight: "bold" },
            maxTicksLimit: 10,
            callback: function(value, index) {
              if (index % Math.ceil(candles.length / 8) === 0 && candles[value]) {
                return dayjs(candles[value].time).format('MM/DD HH:mm');
              }
              return '';
            }
          },
          grid: {
            color: "#E0E0E0",
            lineWidth: 1
          },
        },
        y: {
          position: "right",
          ticks: {
            color: "#000000",
            font: { size: 12, weight: "bold" }
          },
          grid: {
            color: "#E0E0E0",
            lineWidth: 1
          },
        },
      },
    },
  };

  return canvas.renderToBuffer(config, "image/png");
}

async function generateMultiTimeframeChartsForAI(multiTimeframeAnalysis) {
  console.log('📊 Generating AI-optimized charts for visual analysis...');

  const charts = {};
  const chartPromises = [];

  // Generate clean charts for each timeframe
  for (const [timeframe, data] of Object.entries(multiTimeframeAnalysis)) {
    const promise = generateCleanChartForAI(
      data.candles,
      data.indicators,
      timeframe,
      1200,
      800
    ).then(buffer => {
      const filename = `ai-chart-${timeframe}.png`;
      fs.writeFileSync(filename, buffer);
      charts[timeframe] = {
        buffer,
        filename,
        path: `./${filename}`
      };
      console.log(`✅ Generated ${timeframe} chart: ${filename}`);
    });

    chartPromises.push(promise);
  }

  await Promise.all(chartPromises);
  return charts;
}

// === AI Visual Pattern Recognition ===
async function analyzeChartWithAIVision(chartBuffer, timeframe, currentPrice) {
  try {
    console.log(`🔍 Analyzing ${timeframe} chart with AI vision...`);

    // Convert buffer to base64 for OpenAI Vision API
    const base64Image = chartBuffer.toString('base64');

    const prompt = `Bạn là chuyên gia phân tích kỹ thuật chuyên nghiệp. Hãy phân tích biểu đồ nến ${timeframe} của ${SYMBOL} và đưa ra những nhận định chi tiết.

BỐI CẢNH HIỆN TẠI:
- Mã: ${SYMBOL}
- Khung thời gian: ${timeframe}
- Giá hiện tại: ${currentPrice}

YÊU CẦU PHÂN TÍCH:
1. PHÂN TÍCH XU HƯỚNG: Xác định hướng xu hướng tổng thể (tăng, giảm, hoặc sideway)
2. MÔ HÌNH BIỂU ĐỒ: Tìm các mô hình cổ điển (tam giác, cờ, đầu vai, đỉnh/đáy kép, v.v.)
3. MÔ HÌNH NẾN: Xác định các hình thành nến quan trọng (doji, búa, nuốt chửng, v.v.)
4. HỖ TRỢ & KHÁNG CỰ: Đánh dấu các mức ngang quan trọng nơi giá đã phản ứng
5. ĐƯỜNG TRUNG BÌNH ĐỘNG: Phân tích mối quan hệ giữa giá và các đường EMA màu (Xanh=EMA20, Cam=EMA50, Đỏ=EMA200)
6. PHÂN TÍCH KHỐI LƯỢNG: Nhận xét về các mô hình khối lượng nếu có thể nhìn thấy
7. ĐỘNG LỰC: Đánh giá xem động lực đang tăng hay giảm

TRẢ LỜI theo định dạng JSON CHÍNH XÁC này:
{
  "trend": {
    "direction": "BULLISH|BEARISH|SIDEWAYS",
    "strength": 1-10,
    "confidence": 1-10,
    "reasoning": "Brief explanation of trend analysis"
  },
  "patterns": {
    "chart_patterns": ["list of identified chart patterns"],
    "candlestick_patterns": ["list of candlestick patterns"],
    "pattern_significance": "HIGH|MEDIUM|LOW"
  },
  "levels": {
    "support": [list of support price levels],
    "resistance": [list of resistance price levels],
    "key_level_proximity": "How close is current price to key levels"
  },
  "moving_averages": {
    "price_vs_ema20": "ABOVE|BELOW|AT",
    "price_vs_ema50": "ABOVE|BELOW|AT",
    "price_vs_ema200": "ABOVE|BELOW|AT",
    "ema_alignment": "BULLISH|BEARISH|MIXED",
    "ema_analysis": "Brief analysis of EMA positioning"
  },
  "momentum": {
    "direction": "INCREASING|DECREASING|NEUTRAL",
    "strength": 1-10,
    "momentum_analysis": "Analysis of momentum indicators"
  },
  "trading_signal": {
    "signal": "STRONG_BUY|BUY|HOLD|SELL|STRONG_SELL",
    "entry_zone": "Suggested entry price range",
    "stop_loss": "Suggested stop loss level",
    "take_profit": "Suggested take profit levels",
    "risk_reward": "Risk to reward ratio",
    "signal_reasoning": "Why this signal was generated"
  },
  "overall_assessment": {
    "market_condition": "TRENDING|RANGING|VOLATILE|CONSOLIDATING",
    "trade_quality": "HIGH|MEDIUM|LOW",
    "confidence_score": 1-10,
    "key_insights": "Most important observations"
  }
}

QUAN TRỌNG: Chỉ trả lời bằng JSON hợp lệ. Không có văn bản hoặc giải thích bổ sung nào bên ngoài cấu trúc JSON.`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: prompt },
            {
              type: "image_url",
              image_url: {
                url: `data:image/png;base64,${base64Image}`,
                detail: "high"
              }
            }
          ]
        }
      ],
      max_tokens: 2000,
      temperature: 0.1 // Low temperature for consistent analysis
    });

    const analysisText = response.choices[0].message.content;

    // Parse JSON response
    let analysis;
    try {
      analysis = JSON.parse(analysisText);
    } catch (parseError) {
      console.error(`❌ Failed to parse AI analysis for ${timeframe}:`, parseError);
      // Return fallback analysis
      analysis = createFallbackAnalysis(timeframe);
    }

    console.log(`✅ AI analysis completed for ${timeframe}`);
    return analysis;

  } catch (error) {
    console.error(`❌ AI vision analysis failed for ${timeframe}:`, error);
    return createFallbackAnalysis(timeframe);
  }
}

function createFallbackAnalysis(timeframe) {
  return {
    trend: {
      direction: "SIDEWAYS",
      strength: 5,
      confidence: 3,
      reasoning: `AI analysis unavailable for ${timeframe}`
    },
    patterns: {
      chart_patterns: [],
      candlestick_patterns: [],
      pattern_significance: "LOW"
    },
    levels: {
      support: [],
      resistance: [],
      key_level_proximity: "Unknown"
    },
    moving_averages: {
      price_vs_ema20: "AT",
      price_vs_ema50: "AT",
      price_vs_ema200: "AT",
      ema_alignment: "MIXED",
      ema_analysis: "Analysis unavailable"
    },
    momentum: {
      direction: "NEUTRAL",
      strength: 5,
      momentum_analysis: "Analysis unavailable"
    },
    trading_signal: {
      signal: "HOLD",
      entry_zone: "N/A",
      stop_loss: "N/A",
      take_profit: "N/A",
      risk_reward: "N/A",
      signal_reasoning: "AI analysis failed"
    },
    overall_assessment: {
      market_condition: "RANGING",
      trade_quality: "LOW",
      confidence_score: 1,
      key_insights: "AI vision analysis unavailable"
    }
  };
}

// === AI-Powered Multi-Timeframe Analysis ===
async function performAIVisualAnalysis(multiTimeframeAnalysis) {
  try {
    console.log('🤖 Starting AI-powered visual chart analysis...');

    // Generate clean charts for AI analysis
    const charts = await generateMultiTimeframeChartsForAI(multiTimeframeAnalysis);

    // Analyze each timeframe with AI vision
    const aiAnalysis = {};
    const analysisPromises = [];

    for (const [timeframe, chartData] of Object.entries(charts)) {
      const currentPrice = multiTimeframeAnalysis[timeframe].candles.at(-1).close;

      const promise = analyzeChartWithAIVision(
        chartData.buffer,
        timeframe,
        currentPrice
      ).then(analysis => {
        aiAnalysis[timeframe] = analysis;
      });

      analysisPromises.push(promise);
    }

    await Promise.all(analysisPromises);

    console.log('✅ AI visual analysis completed for all timeframes');
    return aiAnalysis;

  } catch (error) {
    console.error('❌ AI visual analysis failed:', error);
    throw error;
  }
}

// === AI-Powered Trading Signal Generation (Replaces Rule-Based Logic) ===
async function generateAITradingSignal(aiAnalysis, multiTimeframeAnalysis) {
  try {
    console.log('🎯 Generating AI-powered trading signal...');

    const { '4h': ai4h, '1h': ai1h, '15m': ai15m } = aiAnalysis;
    const currentPrice = multiTimeframeAnalysis['15m'].candles.at(-1).close;

    // Tạo prompt phân tích toàn diện cho quyết định giao dịch cuối cùng
    const multiTimeframePrompt = `Bạn là chuyên gia phân tích giao dịch chuyên nghiệp đưa ra quyết định giao dịch cuối cùng dựa trên phân tích hình ảnh AI đa khung thời gian.

KẾT QUẢ PHÂN TÍCH AI ĐA KHUNG THỜI GIAN:

PHÂN TÍCH KHUNG 4H:
- Xu hướng: ${ai4h.trend.direction} (Sức mạnh: ${ai4h.trend.strength}/10, Độ tin cậy: ${ai4h.trend.confidence}/10)
- Mô hình: ${ai4h.patterns.chart_patterns.join(', ') || 'Không xác định được'}
- Tín hiệu: ${ai4h.trading_signal.signal}
- Nhận định chính: ${ai4h.overall_assessment.key_insights}

PHÂN TÍCH KHUNG 1H:
- Xu hướng: ${ai1h.trend.direction} (Sức mạnh: ${ai1h.trend.strength}/10, Độ tin cậy: ${ai1h.trend.confidence}/10)
- Mô hình: ${ai1h.patterns.chart_patterns.join(', ') || 'Không xác định được'}
- Tín hiệu: ${ai1h.trading_signal.signal}
- Nhận định chính: ${ai1h.overall_assessment.key_insights}

PHÂN TÍCH KHUNG 15M:
- Xu hướng: ${ai15m.trend.direction} (Sức mạnh: ${ai15m.trend.strength}/10, Độ tin cậy: ${ai15m.trend.confidence}/10)
- Mô hình: ${ai15m.patterns.chart_patterns.join(', ') || 'Không xác định được'}
- Tín hiệu: ${ai15m.trading_signal.signal}
- Nhận định chính: ${ai15m.overall_assessment.key_insights}

DỮ LIỆU THỊ TRƯỜNG HIỆN TẠI:
- Mã: ${SYMBOL}
- Giá hiện tại: ${currentPrice}
- Hỗ trợ/Kháng cự 4H: ${ai4h.levels.support.join(', ')} / ${ai4h.levels.resistance.join(', ')}
- Hỗ trợ/Kháng cự 1H: ${ai1h.levels.support.join(', ')} / ${ai1h.levels.resistance.join(', ')}
- Hỗ trợ/Kháng cự 15M: ${ai15m.levels.support.join(', ')} / ${ai15m.levels.resistance.join(', ')}

KHUNG QUYẾT ĐỊNH GIAO DỊCH:
1. Các khung thời gian cao hơn (4H, 1H) xác định hướng xu hướng chính
2. Khung 15M cung cấp thời điểm vào lệnh và độ chính xác
3. Lý tưởng nhất là tất cả khung thời gian nên thống nhất cho giao dịch tin cậy cao
4. Quản lý rủi ro là tối quan trọng - cần có mức cắt lỗ và chốt lời rõ ràng

TRẢ LỜI với quyết định giao dịch toàn diện theo định dạng JSON CHÍNH XÁC này:
{
  "major_trend": {
    "direction": "BULLISH|BEARISH|SIDEWAYS",
    "strength": 1-10,
    "confidence": 1-10,
    "reasoning": "Analysis of 4H and 1H alignment"
  },
  "timeframe_alignment": {
    "alignment_score": 1-10,
    "alignment_type": "FULLY_ALIGNED|PARTIALLY_ALIGNED|CONFLICTING",
    "alignment_analysis": "How well do all timeframes align"
  },
  "trading_recommendation": {
    "action": "STRONG_BUY|BUY|HOLD|SELL|STRONG_SELL|NO_TRADE",
    "confidence": 1-10,
    "entry_price": "Specific entry price or range",
    "stop_loss": "Specific stop loss level",
    "take_profit_1": "First take profit target",
    "take_profit_2": "Second take profit target",
    "risk_reward_ratio": "Risk to reward ratio",
    "position_size": "Suggested position size (SMALL|MEDIUM|LARGE)",
    "reasoning": "Detailed reasoning for this recommendation"
  },
  "risk_assessment": {
    "risk_level": "LOW|MEDIUM|HIGH",
    "key_risks": ["List of main risks"],
    "invalidation_level": "Price level that invalidates the setup",
    "market_conditions": "Current market environment assessment"
  },
  "execution_plan": {
    "entry_strategy": "How to enter the position",
    "exit_strategy": "How to manage the position",
    "monitoring_points": ["Key levels to watch"],
    "time_horizon": "Expected trade duration"
  },
  "overall_assessment": {
    "trade_quality": "EXCELLENT|GOOD|FAIR|POOR",
    "market_opportunity": 1-10,
    "final_confidence": 1-10,
    "key_message": "Most important takeaway for the trader"
  }
}

QUAN TRỌNG: Chỉ trả lời bằng JSON hợp lệ. Xem xét tất cả khung thời gian nhưng ưu tiên xu hướng khung thời gian cao hơn cho hướng chính.`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "Bạn là chuyên gia phân tích giao dịch chuyên nghiệp với hơn 20 năm kinh nghiệm. Đưa ra các quyết định giao dịch thận trọng, có lý lẽ dựa trên phân tích đa khung thời gian. Ưu tiên quản lý rủi ro và chỉ khuyến nghị các setup có xác suất cao."
        },
        {
          role: "user",
          content: multiTimeframePrompt
        }
      ],
      max_tokens: 2000,
      temperature: 0.1
    });

    const signalText = response.choices[0].message.content;

    let tradingSignal;
    try {
      tradingSignal = JSON.parse(signalText);
    } catch (parseError) {
      console.error('❌ Failed to parse AI trading signal:', parseError);
      tradingSignal = createFallbackTradingSignal();
    }

    // Add metadata
    tradingSignal.metadata = {
      analysis_type: 'AI_VISUAL',
      timestamp: new Date().toISOString(),
      symbol: SYMBOL,
      current_price: currentPrice,
      timeframes_analyzed: ['4h', '1h', '15m']
    };

    console.log('✅ AI trading signal generated successfully');
    return tradingSignal;

  } catch (error) {
    console.error('❌ AI trading signal generation failed:', error);
    return createFallbackTradingSignal();
  }
}

function createFallbackTradingSignal() {
  return {
    major_trend: {
      direction: "SIDEWAYS",
      strength: 5,
      confidence: 3,
      reasoning: "AI analysis unavailable"
    },
    timeframe_alignment: {
      alignment_score: 5,
      alignment_type: "CONFLICTING",
      alignment_analysis: "Unable to determine alignment"
    },
    trading_recommendation: {
      action: "NO_TRADE",
      confidence: 1,
      entry_price: "N/A",
      stop_loss: "N/A",
      take_profit_1: "N/A",
      take_profit_2: "N/A",
      risk_reward_ratio: "N/A",
      position_size: "SMALL",
      reasoning: "AI analysis failed - avoiding risk"
    },
    risk_assessment: {
      risk_level: "HIGH",
      key_risks: ["AI analysis unavailable"],
      invalidation_level: "N/A",
      market_conditions: "Unknown"
    },
    execution_plan: {
      entry_strategy: "Wait for manual analysis",
      exit_strategy: "N/A",
      monitoring_points: [],
      time_horizon: "N/A"
    },
    overall_assessment: {
      trade_quality: "POOR",
      market_opportunity: 1,
      final_confidence: 1,
      key_message: "AI vision analysis failed - manual review required"
    },
    metadata: {
      analysis_type: 'FALLBACK',
      timestamp: new Date().toISOString(),
      symbol: SYMBOL,
      current_price: 0,
      timeframes_analyzed: []
    }
  };
}

// === Enhanced GPT Analysis with AI Vision Results ===
async function analyzeWithAIVision(aiAnalysis, aiTradingSignal) {
  try {
    const { '15m': ai15m, '1h': ai1h, '4h': ai4h } = aiAnalysis;
    const currentPrice = aiTradingSignal.metadata.current_price;

    // Format AI analysis for human-readable output
    const formatTrend = (trend) => {
      const trendEmoji = { 'BULLISH': '🟢', 'BEARISH': '🔴', 'SIDEWAYS': '⚪' };
      return `${trendEmoji[trend.direction]} ${trend.direction}`;
    };

    const formatSignal = (action) => {
      const signalEmoji = {
        'STRONG_BUY': '🟢🟢', 'BUY': '🟢',
        'STRONG_SELL': '🔴🔴', 'SELL': '🔴',
        'HOLD': '⚪', 'NO_TRADE': '⚪'
      };
      return `${signalEmoji[action] || '⚪'} ${action}`;
    };

    const text = `Bạn là chuyên gia phân tích giao dịch chuyên nghiệp tạo báo cáo thị trường toàn diện dựa trên phân tích biểu đồ hình ảnh AI.

KẾT QUẢ PHÂN TÍCH HÌNH ẢNH AI:

XU HƯỚNG ĐA KHUNG THỜI GIAN:
- 4H: ${formatTrend(ai4h.trend)} (Độ tin cậy: ${ai4h.trend.confidence}/10)
- 1H: ${formatTrend(ai1h.trend)} (Độ tin cậy: ${ai1h.trend.confidence}/10)
- 15M: ${formatTrend(ai15m.trend)} (Độ tin cậy: ${ai15m.trend.confidence}/10)

MÔ HÌNH BIỂU ĐỒ ĐƯỢC XÁC ĐỊNH:
- Mô hình 4H: ${ai4h.patterns.chart_patterns.join(', ') || 'Không có'}
- Mô hình 1H: ${ai1h.patterns.chart_patterns.join(', ') || 'Không có'}
- Mô hình 15M: ${ai15m.patterns.chart_patterns.join(', ') || 'Không có'}

KHUYẾN NGHỊ GIAO DỊCH AI:
- Hành động: ${formatSignal(aiTradingSignal.trading_recommendation.action)}
- Độ tin cậy: ${aiTradingSignal.trading_recommendation.confidence}/10
- Vào lệnh: ${aiTradingSignal.trading_recommendation.entry_price}
- Cắt lỗ: ${aiTradingSignal.trading_recommendation.stop_loss}
- Chốt lời: ${aiTradingSignal.trading_recommendation.take_profit_1}
- Tỷ lệ R/R: ${aiTradingSignal.trading_recommendation.risk_reward_ratio}

ĐIỀU KIỆN THỊ TRƯỜNG:
- Xu hướng chính: ${formatTrend(aiTradingSignal.major_trend)} (Sức mạnh: ${aiTradingSignal.major_trend.strength}/10)
- Sự thống nhất: ${aiTradingSignal.timeframe_alignment.alignment_type}
- Chất lượng giao dịch: ${aiTradingSignal.overall_assessment.trade_quality}

Tạo báo cáo giao dịch chuyên nghiệp theo định dạng HTML CHÍNH XÁC này:

<b>🤖 PHÂN TÍCH HÌNH ẢNH AI:</b>
• <b>Xu hướng chính:</b> [${aiTradingSignal.major_trend.direction}] - [Lý do ngắn gọn]
• <b>Đồng bộ khung thời gian:</b> [Trạng thái và chất lượng thống nhất]
• <b>Mô hình chính:</b> [Các mô hình quan trọng nhất được tìm thấy]

<b>🎯 TÍN HIỆU GIAO DỊCH AI:</b>
• <b>Hành động:</b> [${aiTradingSignal.trading_recommendation.action}]
• <b>Vùng vào lệnh:</b> [Giá/khoảng vào lệnh]
• <b>Quản lý rủi ro:</b> SL ${aiTradingSignal.trading_recommendation.stop_loss} | TP ${aiTradingSignal.trading_recommendation.take_profit_1}
• <b>Tỷ lệ R/R:</b> ${aiTradingSignal.trading_recommendation.risk_reward_ratio}

<b>💡 Độ tin cậy AI:</b> ${aiTradingSignal.overall_assessment.final_confidence}/10

<b>🔍 Nhận định chính:</b> ${aiTradingSignal.overall_assessment.key_message}

QUAN TRỌNG: Chỉ sử dụng thẻ HTML <b></b>. Ngắn gọn và chuyên nghiệp. Tối đa 400 ký tự.`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: "Bạn là chuyên gia phân tích giao dịch chuyên nghiệp. Tạo báo cáo giao dịch ngắn gọn, chuyên nghiệp dựa trên phân tích hình ảnh AI. Chỉ sử dụng thẻ HTML <b></b>, không dùng markdown. Tập trung vào những nhận định có thể hành động được."
        },
        {
          role: "user",
          content: text
        }
      ],
      max_tokens: 800,
      temperature: 0.1
    });

    return response.choices[0].message.content;

  } catch (error) {
    console.error('❌ Enhanced GPT analysis failed:', error);
    return `<b>⚠️ AI ANALYSIS ERROR</b>\n\nAI visual analysis encountered an error. Please check system logs.\n\n<b>Status:</b> Manual review required`;
  }
}
