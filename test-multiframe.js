import dotenv from "dotenv";
import { 
  fetchAllTimeframes, 
  performMultiTimeframeAnalysis, 
  generateTradingSignal,
  calculateIndicators,
  analyzeTrend 
} from "./index.js";

dotenv.config();

// === Test Multi-Timeframe System ===
async function testMultiTimeframeSystem() {
  console.log('🧪 Testing Multi-Timeframe Analysis System...\n');
  
  try {
    // Test 1: Data Fetching
    console.log('📊 Test 1: Multi-Timeframe Data Fetching');
    const startTime = Date.now();
    const allData = await fetchAllTimeframes();
    const fetchTime = Date.now() - startTime;
    
    console.log(`✅ Data fetched in ${fetchTime}ms`);
    console.log(`   - 15m candles: ${allData['15m'].length}`);
    console.log(`   - 1h candles: ${allData['1h'].length}`);
    console.log(`   - 4h candles: ${allData['4h'].length}\n`);
    
    // Test 2: Trend Analysis
    console.log('📈 Test 2: Trend Analysis for Each Timeframe');
    const trendResults = {};
    
    for (const [timeframe, candles] of Object.entries(allData)) {
      const indicators = calculateIndicators(candles);
      const trend = analyzeTrend(candles, indicators);
      trendResults[timeframe] = trend;
      
      console.log(`   ${timeframe.toUpperCase()}: ${trend.trend} (Strength: ${(trend.strength * 100).toFixed(1)}%, Confidence: ${(trend.confidence * 100).toFixed(1)}%)`);
    }
    console.log('');
    
    // Test 3: Multi-Timeframe Analysis
    console.log('🔄 Test 3: Complete Multi-Timeframe Analysis');
    const analysisStartTime = Date.now();
    const multiTimeframeAnalysis = await performMultiTimeframeAnalysis();
    const analysisTime = Date.now() - analysisStartTime;
    
    console.log(`✅ Analysis completed in ${analysisTime}ms`);
    console.log('   Timeframe trends:');
    Object.entries(multiTimeframeAnalysis).forEach(([tf, data]) => {
      console.log(`   - ${tf.toUpperCase()}: ${data.trend.trend} (${(data.trend.confidence * 100).toFixed(0)}%)`);
    });
    console.log('');
    
    // Test 4: Trading Signal Generation
    console.log('🎯 Test 4: Trading Signal Generation');
    const tradingSignal = generateTradingSignal(multiTimeframeAnalysis);
    
    console.log(`   Major Trend: ${tradingSignal.majorTrend.trend} (${(tradingSignal.majorTrend.strength * 100).toFixed(1)}%)`);
    console.log(`   Alignment: ${tradingSignal.trendAlignment.alignment} (${(tradingSignal.trendAlignment.strength * 100).toFixed(1)}%)`);
    console.log(`   Entry Signal: ${tradingSignal.entrySignal.signal}`);
    console.log(`   Recommendation: ${tradingSignal.recommendation.action}`);
    console.log(`   Overall Confidence: ${(tradingSignal.confidence * 100).toFixed(1)}%`);
    
    if (tradingSignal.riskLevels) {
      console.log(`   Risk Management:`);
      console.log(`     - Support: ${tradingSignal.riskLevels.support?.toFixed(4)}`);
      console.log(`     - Resistance: ${tradingSignal.riskLevels.resistance?.toFixed(4)}`);
      console.log(`     - Stop Loss: ${tradingSignal.riskLevels.stopLoss?.toFixed(4)}`);
      console.log(`     - Take Profit 1: ${tradingSignal.riskLevels.takeProfit1?.toFixed(4)}`);
      console.log(`     - Risk/Reward: 1:${tradingSignal.riskLevels.riskReward?.toFixed(2)}`);
    }
    console.log('');
    
    // Test 5: Performance Metrics
    console.log('⚡ Test 5: Performance Metrics');
    console.log(`   Total execution time: ${Date.now() - startTime}ms`);
    console.log(`   Data fetch efficiency: ${(allData['15m'].length + allData['1h'].length + allData['4h'].length)} candles in ${fetchTime}ms`);
    console.log(`   Analysis efficiency: ${analysisTime}ms for 3 timeframes`);
    
    // Test 6: Data Quality Validation
    console.log('\n🔍 Test 6: Data Quality Validation');
    validateDataQuality(allData);
    
    console.log('\n✅ All tests completed successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
    return false;
  }
}

function validateDataQuality(allData) {
  Object.entries(allData).forEach(([timeframe, candles]) => {
    const issues = [];
    
    // Check for missing data
    if (candles.length === 0) {
      issues.push('No candles found');
    }
    
    // Check for data consistency
    let invalidCandles = 0;
    let missingVolume = 0;
    
    candles.forEach((candle, index) => {
      if (!candle.open || !candle.high || !candle.low || !candle.close) {
        invalidCandles++;
      }
      if (!candle.volume || candle.volume === 0) {
        missingVolume++;
      }
      if (candle.high < candle.low) {
        issues.push(`Invalid OHLC at index ${index}: High < Low`);
      }
      if (candle.open < 0 || candle.close < 0) {
        issues.push(`Negative prices at index ${index}`);
      }
    });
    
    // Check time sequence
    let timeSequenceErrors = 0;
    for (let i = 1; i < candles.length; i++) {
      if (candles[i].time <= candles[i-1].time) {
        timeSequenceErrors++;
      }
    }
    
    console.log(`   ${timeframe.toUpperCase()}:`);
    console.log(`     - Total candles: ${candles.length}`);
    console.log(`     - Invalid candles: ${invalidCandles}`);
    console.log(`     - Missing volume: ${missingVolume}`);
    console.log(`     - Time sequence errors: ${timeSequenceErrors}`);
    
    if (issues.length > 0) {
      console.log(`     - Issues: ${issues.join(', ')}`);
    } else {
      console.log(`     - ✅ Data quality: Good`);
    }
  });
}

// === Run Tests ===
if (import.meta.url === `file://${process.argv[1]}`) {
  testMultiTimeframeSystem()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test runner error:', error);
      process.exit(1);
    });
}

export { testMultiTimeframeSystem, validateDataQuality };
