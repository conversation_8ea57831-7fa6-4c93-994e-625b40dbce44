import dotenv from "dotenv";
import fs from "fs";
import { 
  performMultiTimeframeAnalysis,
  generateMultiTimeframeChartsForAI,
  analyzeChartWithAIVision,
  performAIVisualAnalysis,
  generateAITradingSignal,
  analyzeWithAIVision
} from "./index.js";

dotenv.config();

// === Test AI Vision Trading System ===
async function testAIVisionSystem() {
  console.log('🤖 Testing AI Vision Trading System...\n');
  
  try {
    // Test 1: Multi-Timeframe Data Collection
    console.log('📊 Test 1: Multi-Timeframe Data Collection');
    const startTime = Date.now();
    const multiTimeframeAnalysis = await performMultiTimeframeAnalysis();
    const dataTime = Date.now() - startTime;
    
    console.log(`✅ Data collected in ${dataTime}ms`);
    console.log(`   - 15m candles: ${multiTimeframeAnalysis['15m'].candles.length}`);
    console.log(`   - 1h candles: ${multiTimeframeAnalysis['1h'].candles.length}`);
    console.log(`   - 4h candles: ${multiTimeframeAnalysis['4h'].candles.length}\n`);
    
    // Test 2: AI-Optimized Chart Generation
    console.log('🎨 Test 2: AI-Optimized Chart Generation');
    const chartStartTime = Date.now();
    const charts = await generateMultiTimeframeChartsForAI(multiTimeframeAnalysis);
    const chartTime = Date.now() - chartStartTime;
    
    console.log(`✅ Charts generated in ${chartTime}ms`);
    Object.entries(charts).forEach(([timeframe, chartData]) => {
      const fileSize = fs.statSync(chartData.filename).size;
      console.log(`   - ${timeframe}: ${chartData.filename} (${(fileSize/1024).toFixed(1)}KB)`);
    });
    console.log('');
    
    // Test 3: Individual Chart AI Analysis
    console.log('🔍 Test 3: Individual Chart AI Analysis');
    const analysisResults = {};
    
    for (const [timeframe, chartData] of Object.entries(charts)) {
      const currentPrice = multiTimeframeAnalysis[timeframe].candles.at(-1).close;
      console.log(`   Analyzing ${timeframe} chart...`);
      
      const analysisStart = Date.now();
      const analysis = await analyzeChartWithAIVision(chartData.buffer, timeframe, currentPrice);
      const analysisTime = Date.now() - analysisStart;
      
      analysisResults[timeframe] = analysis;
      
      console.log(`   ✅ ${timeframe} analysis completed in ${analysisTime}ms`);
      console.log(`      - Trend: ${analysis.trend.direction} (${analysis.trend.confidence}/10)`);
      console.log(`      - Signal: ${analysis.trading_signal.signal}`);
      console.log(`      - Patterns: ${analysis.patterns.chart_patterns.join(', ') || 'None'}`);
    }
    console.log('');
    
    // Test 4: Complete AI Visual Analysis
    console.log('🤖 Test 4: Complete AI Visual Analysis');
    const fullAnalysisStart = Date.now();
    const aiAnalysis = await performAIVisualAnalysis(multiTimeframeAnalysis);
    const fullAnalysisTime = Date.now() - fullAnalysisStart;
    
    console.log(`✅ Full AI analysis completed in ${fullAnalysisTime}ms`);
    console.log('   AI Analysis Summary:');
    Object.entries(aiAnalysis).forEach(([timeframe, analysis]) => {
      console.log(`   - ${timeframe.toUpperCase()}: ${analysis.trend.direction} (Confidence: ${analysis.trend.confidence}/10)`);
    });
    console.log('');
    
    // Test 5: AI Trading Signal Generation
    console.log('🎯 Test 5: AI Trading Signal Generation');
    const signalStart = Date.now();
    const aiTradingSignal = await generateAITradingSignal(aiAnalysis, multiTimeframeAnalysis);
    const signalTime = Date.now() - signalStart;
    
    console.log(`✅ AI trading signal generated in ${signalTime}ms`);
    console.log('   Trading Signal Summary:');
    console.log(`   - Major Trend: ${aiTradingSignal.major_trend.direction} (${aiTradingSignal.major_trend.strength}/10)`);
    console.log(`   - Alignment: ${aiTradingSignal.timeframe_alignment.alignment_type}`);
    console.log(`   - Recommendation: ${aiTradingSignal.trading_recommendation.action}`);
    console.log(`   - Confidence: ${aiTradingSignal.trading_recommendation.confidence}/10`);
    console.log(`   - Entry: ${aiTradingSignal.trading_recommendation.entry_price}`);
    console.log(`   - Stop Loss: ${aiTradingSignal.trading_recommendation.stop_loss}`);
    console.log(`   - Take Profit: ${aiTradingSignal.trading_recommendation.take_profit_1}`);
    console.log(`   - Risk/Reward: ${aiTradingSignal.trading_recommendation.risk_reward_ratio}`);
    console.log('');
    
    // Test 6: Enhanced GPT Analysis
    console.log('📝 Test 6: Enhanced GPT Analysis');
    const gptStart = Date.now();
    const gptAnalysis = await analyzeWithAIVision(aiAnalysis, aiTradingSignal);
    const gptTime = Date.now() - gptStart;
    
    console.log(`✅ GPT analysis completed in ${gptTime}ms`);
    console.log('   GPT Analysis Output:');
    console.log(gptAnalysis);
    console.log('');
    
    // Test 7: Performance Metrics
    console.log('⚡ Test 7: Performance Metrics');
    const totalTime = Date.now() - startTime;
    console.log(`   Total execution time: ${totalTime}ms`);
    console.log(`   Data collection: ${dataTime}ms (${((dataTime/totalTime)*100).toFixed(1)}%)`);
    console.log(`   Chart generation: ${chartTime}ms (${((chartTime/totalTime)*100).toFixed(1)}%)`);
    console.log(`   AI analysis: ${fullAnalysisTime}ms (${((fullAnalysisTime/totalTime)*100).toFixed(1)}%)`);
    console.log(`   Signal generation: ${signalTime}ms (${((signalTime/totalTime)*100).toFixed(1)}%)`);
    console.log(`   GPT analysis: ${gptTime}ms (${((gptTime/totalTime)*100).toFixed(1)}%)`);
    console.log('');
    
    // Test 8: Quality Assessment
    console.log('🔍 Test 8: Quality Assessment');
    assessAIQuality(aiAnalysis, aiTradingSignal);
    
    // Cleanup generated files
    console.log('🧹 Cleaning up generated files...');
    Object.values(charts).forEach(chart => {
      try {
        fs.unlinkSync(chart.filename);
        console.log(`   Deleted: ${chart.filename}`);
      } catch (error) {
        console.log(`   Failed to delete: ${chart.filename}`);
      }
    });
    
    console.log('\n✅ All AI vision tests completed successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ AI vision test failed:', error);
    console.error('Stack trace:', error.stack);
    return false;
  }
}

function assessAIQuality(aiAnalysis, aiTradingSignal) {
  console.log('   AI Quality Assessment:');
  
  // Check trend consistency across timeframes
  const trends = Object.values(aiAnalysis).map(a => a.trend.direction);
  const uniqueTrends = [...new Set(trends)];
  const trendConsistency = uniqueTrends.length === 1 ? 'PERFECT' : 
                          uniqueTrends.length === 2 ? 'GOOD' : 'MIXED';
  
  console.log(`   - Trend Consistency: ${trendConsistency} (${uniqueTrends.join(', ')})`);
  
  // Check confidence levels
  const confidences = Object.values(aiAnalysis).map(a => a.trend.confidence);
  const avgConfidence = confidences.reduce((sum, c) => sum + c, 0) / confidences.length;
  const confidenceQuality = avgConfidence >= 8 ? 'HIGH' : avgConfidence >= 6 ? 'MEDIUM' : 'LOW';
  
  console.log(`   - Average Confidence: ${avgConfidence.toFixed(1)}/10 (${confidenceQuality})`);
  
  // Check pattern detection
  const allPatterns = Object.values(aiAnalysis).flatMap(a => a.patterns.chart_patterns);
  const patternCount = allPatterns.length;
  const patternQuality = patternCount >= 3 ? 'RICH' : patternCount >= 1 ? 'MODERATE' : 'MINIMAL';
  
  console.log(`   - Pattern Detection: ${patternCount} patterns found (${patternQuality})`);
  
  // Check trading signal quality
  const signalQuality = aiTradingSignal.overall_assessment.trade_quality;
  const finalConfidence = aiTradingSignal.overall_assessment.final_confidence;
  
  console.log(`   - Signal Quality: ${signalQuality} (${finalConfidence}/10 confidence)`);
  
  // Overall assessment
  const qualityScore = (avgConfidence + finalConfidence) / 2;
  const overallQuality = qualityScore >= 8 ? 'EXCELLENT' : 
                        qualityScore >= 6 ? 'GOOD' : 
                        qualityScore >= 4 ? 'FAIR' : 'POOR';
  
  console.log(`   - Overall Quality: ${overallQuality} (${qualityScore.toFixed(1)}/10)`);
}

// === Run Tests ===
if (import.meta.url === `file://${process.argv[1]}`) {
  testAIVisionSystem()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test runner error:', error);
      process.exit(1);
    });
}

export { testAIVisionSystem, assessAIQuality };
